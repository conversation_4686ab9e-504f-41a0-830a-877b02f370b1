[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "jump_time_stone_3", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 4}, {"__id__": 12}, {"__id__": 18}, {"__id__": 24}, {"__id__": 30}, {"__id__": 36}, {"__id__": 40}, {"__id__": 58}, {"__id__": 76}, {"__id__": 94}], "_active": true, "_components": [{"__id__": 96}, {"__id__": 97}], "_prefab": {"__id__": 98}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 5214.02, "height": 2097.04}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.06}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "start", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 3}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1.202, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "96uoZnwIFMrKJ+4SJxuzBW", "sync": false}, {"__type__": "cc.Node", "_name": "rebirth2", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 5}, {"__id__": 8}], "_active": true, "_components": [{"__id__": 10}], "_prefab": {"__id__": 11}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 334, "height": 156}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [716.367, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "fuhuodian02_dimian", "_objFlags": 512, "_parent": {"__id__": 4}, "_children": [], "_active": true, "_components": [{"__id__": 6}], "_prefab": {"__id__": 7}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 313, "height": 534}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 156, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 5}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "57931b4e-9b6e-41cd-a8f1-51cc260bc014"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 4}, "asset": {"__uuid__": "d1eaa193-4071-4d4f-a563-c52f4dc31f69"}, "fileId": "9crJzGO+BOiL34GXhyVubm", "sync": false}, {"__type__": "cc.Node", "_name": "line", "_objFlags": 512, "_parent": {"__id__": 4}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 9}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 334, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 156, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 4}, "asset": {"__uuid__": "d1eaa193-4071-4d4f-a563-c52f4dc31f69"}, "fileId": "d9sJ4mEgxAE7igXFJzBSVW", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 4}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "2f998ae1-7137-4bb2-b712-57c9cbd711ab"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 4}, "asset": {"__uuid__": "d1eaa193-4071-4d4f-a563-c52f4dc31f69"}, "fileId": "5aGph53pBGH6Yym3MG5l4m", "sync": false}, {"__type__": "cc.Node", "_name": "normal1", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 13}], "_active": true, "_components": [{"__id__": 15}, {"__id__": 16}], "_prefab": {"__id__": 17}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 90, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1266.021, 95.106, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "line", "_objFlags": 512, "_parent": {"__id__": 12}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 14}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 90, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 69, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 12}, "asset": {"__uuid__": "a8333c11-329b-4839-9807-eeb90815c34e"}, "fileId": "71kbV8/ANFIqaj/LfJU35s", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "af0ef419-e7cc-4941-af73-e72a97006783"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": 40.4, "y": 69.3}, {"__type__": "cc.Vec2", "x": -44.3, "y": 68.8}, {"__type__": "cc.Vec2", "x": -36.1, "y": 14.9}, {"__type__": "cc.Vec2", "x": -17.7, "y": 0.1}, {"__type__": "cc.Vec2", "x": 14.7, "y": 0}, {"__type__": "cc.Vec2", "x": 35.6, "y": 29.8}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 12}, "asset": {"__uuid__": "a8333c11-329b-4839-9807-eeb90815c34e"}, "fileId": "16pLVZdPZG7JZZDdj91hFO", "sync": false}, {"__type__": "cc.Node", "_name": "normal4", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 19}], "_active": true, "_components": [{"__id__": 21}, {"__id__": 22}], "_prefab": {"__id__": 23}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 308, "height": 176}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [2221.646, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "line", "_objFlags": 512, "_parent": {"__id__": 18}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 20}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 308, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 122, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 18}, "asset": {"__uuid__": "b534759e-35a5-4694-81de-d293ffaf2d66"}, "fileId": "eaq5EICG9KQbo+oQaN8B40", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ca3a4493-198d-4447-905f-ad361401bbac"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": 152.3, "y": 121.7}, {"__type__": "cc.Vec2", "x": -152.8, "y": 122.1}, {"__type__": "cc.Vec2", "x": -146.1, "y": 59.3}, {"__type__": "cc.Vec2", "x": -16.2, "y": 0.1}, {"__type__": "cc.Vec2", "x": 78.5, "y": 0.3}, {"__type__": "cc.Vec2", "x": 134.5, "y": 63.1}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 18}, "asset": {"__uuid__": "b534759e-35a5-4694-81de-d293ffaf2d66"}, "fileId": "32/wZymvhH65vSy3GvwUy9", "sync": false}, {"__type__": "cc.Node", "_name": "normal3", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 25}], "_active": true, "_components": [{"__id__": 27}, {"__id__": 28}], "_prefab": {"__id__": 29}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 204, "height": 133}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [3451.566, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "line", "_objFlags": 512, "_parent": {"__id__": 24}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 26}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 204, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 118, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 24}, "asset": {"__uuid__": "99db765f-738b-41c6-859b-00d493839a63"}, "fileId": "3372cb1iJGxYnMfzPlumBE", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 24}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "6f0b867c-0448-41ac-b7f0-c1af17551886"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 24}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": 100.7, "y": 118.2}, {"__type__": "cc.Vec2", "x": -101.1, "y": 117.4}, {"__type__": "cc.Vec2", "x": -92.3, "y": 53.1}, {"__type__": "cc.Vec2", "x": -44.1, "y": 0.1}, {"__type__": "cc.Vec2", "x": 33.8, "y": 0.8}, {"__type__": "cc.Vec2", "x": 84.5, "y": 53.9}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 24}, "asset": {"__uuid__": "99db765f-738b-41c6-859b-00d493839a63"}, "fileId": "61lgw3cB1I8ZS34DAwASVd", "sync": false}, {"__type__": "cc.Node", "_name": "normal2", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 31}], "_active": true, "_components": [{"__id__": 33}, {"__id__": 34}], "_prefab": {"__id__": 35}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 165, "height": 138}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [4365.431, -122.686, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "line", "_objFlags": 512, "_parent": {"__id__": 30}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 32}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 165, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 30}, "asset": {"__uuid__": "f5de35c7-5d5c-4d36-a601-8a8997fdc309"}, "fileId": "83yIsknl9LsYoFZ2kpKSbV", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 30}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "83d16649-6f52-4dbb-8650-5e82547eafe1"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 30}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": 80, "y": 120.3}, {"__type__": "cc.Vec2", "x": -79.4, "y": 119.8}, {"__type__": "cc.Vec2", "x": -72.7, "y": 57.3}, {"__type__": "cc.Vec2", "x": -34.6, "y": 0.1}, {"__type__": "cc.Vec2", "x": 19.1, "y": 0}, {"__type__": "cc.Vec2", "x": 61.4, "y": 48.7}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 30}, "asset": {"__uuid__": "f5de35c7-5d5c-4d36-a601-8a8997fdc309"}, "fileId": "30nDbIlSdFWZ1p6FvjkS17", "sync": false}, {"__type__": "cc.Node", "_name": "die4", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 37}, {"__id__": 38}], "_prefab": {"__id__": 39}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 444, "height": 151}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [2864.835, 142.819, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 36}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "cff5238d-c051-470f-9b5c-e1b021821436"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 36}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": 210, "y": 137}, {"__type__": "cc.Vec2", "x": -210, "y": 137}, {"__type__": "cc.Vec2", "x": -206.3, "y": 69.7}, {"__type__": "cc.Vec2", "x": -107, "y": 11.6}, {"__type__": "cc.Vec2", "x": 34.6, "y": 11.3}, {"__type__": "cc.Vec2", "x": 93.1, "y": 40.8}, {"__type__": "cc.Vec2", "x": 182.3, "y": 70}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 36}, "asset": {"__uuid__": "084a273a-9ae6-4f9a-81a9-af3c5a84a6ef"}, "fileId": "9fJg47juJIWr5YYtnBqll+", "sync": false}, {"__type__": "cc.Node", "_name": "water", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 41}, {"__id__": 43}, {"__id__": 51}], "_active": true, "_components": [{"__id__": 53}, {"__id__": 54}, {"__id__": 55}, {"__id__": 56}], "_prefab": {"__id__": 57}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 390, "height": 1172.4600830078125}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1761.426, 577, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "box", "_objFlags": 512, "_parent": {"__id__": 40}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 42}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 1020}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 374, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 40}, "asset": {"__uuid__": "707075f7-da7d-45f9-9d0f-6e33920f5c45"}, "fileId": "fcusNBF8VKtLg3w2YdY9Aw", "sync": false}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE_TREE", "_objFlags": 512, "_parent": {"__id__": 40}, "_children": [{"__id__": 44}], "_active": true, "_components": [], "_prefab": {"__id__": 50}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:root", "_objFlags": 0, "_parent": {"__id__": 43}, "_children": [{"__id__": 45}, {"__id__": 47}], "_active": true, "_components": [], "_prefab": {"__id__": 49}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:guadian_enter", "_objFlags": 0, "_parent": {"__id__": 44}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 46}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 40}, "asset": {"__uuid__": "707075f7-da7d-45f9-9d0f-6e33920f5c45"}, "fileId": "adZW31+KRMgKfaYpU61xB9", "sync": false}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:guadian_exit", "_objFlags": 0, "_parent": {"__id__": 44}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 48}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 40}, "asset": {"__uuid__": "707075f7-da7d-45f9-9d0f-6e33920f5c45"}, "fileId": "9abrfUMFpGGJ9eAiNEBqcZ", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 40}, "asset": {"__uuid__": "707075f7-da7d-45f9-9d0f-6e33920f5c45"}, "fileId": "009QQ7IwVF0rg0fbOheTpw", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 40}, "asset": {"__uuid__": "707075f7-da7d-45f9-9d0f-6e33920f5c45"}, "fileId": "113l4IBNtJmapIQY7NeTWf", "sync": false}, {"__type__": "cc.Node", "_name": "attach", "_objFlags": 512, "_parent": {"__id__": 40}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 52}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 40}, "asset": {"__uuid__": "707075f7-da7d-45f9-9d0f-6e33920f5c45"}, "fileId": "66qDEQguRDJ7jWMl3kEsSs", "sync": false}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 40}, "_enabled": true, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "defaultSkin": "default", "defaultAnimation": "enter", "_preCacheMode": 0, "_cacheMode": 0, "loop": true, "premultipliedAlpha": false, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "enter", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 1, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "55daed05-4f09-4e02-98d6-c0f65ccbfb15"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$debugMesh": false, "_N$useTint": false, "_N$enableBatch": false, "_id": ""}, {"__type__": "7af8fae0vJHbrAJrCAJ0LNj", "_name": "", "_objFlags": 0, "node": {"__id__": 40}, "_enabled": true, "duration": 2, "events": [], "_percent": 0, "_play": true, "_id": ""}, {"__type__": "7c683oKQAZFvLiORvSHlWoC", "_name": "", "_objFlags": 0, "node": {"__id__": 40}, "_enabled": true, "points": [{"__id__": 41}, {"__id__": 45}, {"__id__": 47}, {"__id__": 51}], "_id": ""}, {"__type__": "95f12NtBhJIGbKKHXxNIp2V", "_name": "", "_objFlags": 0, "node": {"__id__": 40}, "_enabled": true, "interval": 1, "duration": 1, "delay": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 40}, "asset": {"__uuid__": "707075f7-da7d-45f9-9d0f-6e33920f5c45"}, "fileId": "73mU97ozxPhpUGn4v4S5YD", "sync": false}, {"__type__": "cc.Node", "_name": "water", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 59}, {"__id__": 61}, {"__id__": 69}], "_active": true, "_components": [{"__id__": 71}, {"__id__": 72}, {"__id__": 73}, {"__id__": 74}], "_prefab": {"__id__": 75}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 390, "height": 1172.4600830078125}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [2832.145, 580.357, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "box", "_objFlags": 512, "_parent": {"__id__": 58}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 60}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 1020}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 374, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 58}, "asset": {"__uuid__": "707075f7-da7d-45f9-9d0f-6e33920f5c45"}, "fileId": "fcusNBF8VKtLg3w2YdY9Aw", "sync": false}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE_TREE", "_objFlags": 512, "_parent": {"__id__": 58}, "_children": [{"__id__": 62}], "_active": true, "_components": [], "_prefab": {"__id__": 68}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:root", "_objFlags": 0, "_parent": {"__id__": 61}, "_children": [{"__id__": 63}, {"__id__": 65}], "_active": true, "_components": [], "_prefab": {"__id__": 67}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:guadian_enter", "_objFlags": 0, "_parent": {"__id__": 62}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 64}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 58}, "asset": {"__uuid__": "707075f7-da7d-45f9-9d0f-6e33920f5c45"}, "fileId": "adZW31+KRMgKfaYpU61xB9", "sync": false}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:guadian_exit", "_objFlags": 0, "_parent": {"__id__": 62}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 66}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 58}, "asset": {"__uuid__": "707075f7-da7d-45f9-9d0f-6e33920f5c45"}, "fileId": "9abrfUMFpGGJ9eAiNEBqcZ", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 58}, "asset": {"__uuid__": "707075f7-da7d-45f9-9d0f-6e33920f5c45"}, "fileId": "009QQ7IwVF0rg0fbOheTpw", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 58}, "asset": {"__uuid__": "707075f7-da7d-45f9-9d0f-6e33920f5c45"}, "fileId": "113l4IBNtJmapIQY7NeTWf", "sync": false}, {"__type__": "cc.Node", "_name": "attach", "_objFlags": 512, "_parent": {"__id__": 58}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 70}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 58}, "asset": {"__uuid__": "707075f7-da7d-45f9-9d0f-6e33920f5c45"}, "fileId": "66qDEQguRDJ7jWMl3kEsSs", "sync": false}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 58}, "_enabled": true, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "defaultSkin": "default", "defaultAnimation": "enter", "_preCacheMode": 0, "_cacheMode": 0, "loop": true, "premultipliedAlpha": false, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "enter", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 1, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "55daed05-4f09-4e02-98d6-c0f65ccbfb15"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$debugMesh": false, "_N$useTint": false, "_N$enableBatch": false, "_id": ""}, {"__type__": "7af8fae0vJHbrAJrCAJ0LNj", "_name": "", "_objFlags": 0, "node": {"__id__": 58}, "_enabled": true, "duration": 2, "events": [], "_percent": 0, "_play": true, "_id": ""}, {"__type__": "7c683oKQAZFvLiORvSHlWoC", "_name": "", "_objFlags": 0, "node": {"__id__": 58}, "_enabled": true, "points": [{"__id__": 59}, {"__id__": 63}, {"__id__": 65}, {"__id__": 69}], "_id": ""}, {"__type__": "95f12NtBhJIGbKKHXxNIp2V", "_name": "", "_objFlags": 0, "node": {"__id__": 58}, "_enabled": true, "interval": 0.5, "duration": 1, "delay": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 58}, "asset": {"__uuid__": "707075f7-da7d-45f9-9d0f-6e33920f5c45"}, "fileId": "b4jU909qRO2KmZsb229WDB", "sync": false}, {"__type__": "cc.Node", "_name": "water", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 77}, {"__id__": 79}, {"__id__": 87}], "_active": true, "_components": [{"__id__": 89}, {"__id__": 90}, {"__id__": 91}, {"__id__": 92}], "_prefab": {"__id__": 93}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 390, "height": 1172.4600830078125}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [3978.32, 577, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "box", "_objFlags": 512, "_parent": {"__id__": 76}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 78}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 1020}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 374, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 76}, "asset": {"__uuid__": "707075f7-da7d-45f9-9d0f-6e33920f5c45"}, "fileId": "fcusNBF8VKtLg3w2YdY9Aw", "sync": false}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE_TREE", "_objFlags": 512, "_parent": {"__id__": 76}, "_children": [{"__id__": 80}], "_active": true, "_components": [], "_prefab": {"__id__": 86}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:root", "_objFlags": 0, "_parent": {"__id__": 79}, "_children": [{"__id__": 81}, {"__id__": 83}], "_active": true, "_components": [], "_prefab": {"__id__": 85}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:guadian_enter", "_objFlags": 0, "_parent": {"__id__": 80}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 82}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 76}, "asset": {"__uuid__": "707075f7-da7d-45f9-9d0f-6e33920f5c45"}, "fileId": "adZW31+KRMgKfaYpU61xB9", "sync": false}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:guadian_exit", "_objFlags": 0, "_parent": {"__id__": 80}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 84}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 76}, "asset": {"__uuid__": "707075f7-da7d-45f9-9d0f-6e33920f5c45"}, "fileId": "9abrfUMFpGGJ9eAiNEBqcZ", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 76}, "asset": {"__uuid__": "707075f7-da7d-45f9-9d0f-6e33920f5c45"}, "fileId": "009QQ7IwVF0rg0fbOheTpw", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 76}, "asset": {"__uuid__": "707075f7-da7d-45f9-9d0f-6e33920f5c45"}, "fileId": "113l4IBNtJmapIQY7NeTWf", "sync": false}, {"__type__": "cc.Node", "_name": "attach", "_objFlags": 512, "_parent": {"__id__": 76}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 88}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 76}, "asset": {"__uuid__": "707075f7-da7d-45f9-9d0f-6e33920f5c45"}, "fileId": "66qDEQguRDJ7jWMl3kEsSs", "sync": false}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 76}, "_enabled": true, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "defaultSkin": "default", "defaultAnimation": "enter", "_preCacheMode": 0, "_cacheMode": 0, "loop": true, "premultipliedAlpha": false, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "enter", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 1, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "55daed05-4f09-4e02-98d6-c0f65ccbfb15"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$debugMesh": false, "_N$useTint": false, "_N$enableBatch": false, "_id": ""}, {"__type__": "7af8fae0vJHbrAJrCAJ0LNj", "_name": "", "_objFlags": 0, "node": {"__id__": 76}, "_enabled": true, "duration": 2, "events": [], "_percent": 0, "_play": true, "_id": ""}, {"__type__": "7c683oKQAZFvLiORvSHlWoC", "_name": "", "_objFlags": 0, "node": {"__id__": 76}, "_enabled": true, "points": [{"__id__": 77}, {"__id__": 81}, {"__id__": 83}, {"__id__": 87}], "_id": ""}, {"__type__": "95f12NtBhJIGbKKHXxNIp2V", "_name": "", "_objFlags": 0, "node": {"__id__": 76}, "_enabled": true, "interval": 2, "duration": 2, "delay": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 76}, "asset": {"__uuid__": "707075f7-da7d-45f9-9d0f-6e33920f5c45"}, "fileId": "8b+ivL0tlNapKuJEVAnz43", "sync": false}, {"__type__": "cc.Node", "_name": "end", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 95}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [5212.813, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 94}, "asset": {"__uuid__": "86139bf8-0dbf-47cc-ad19-3ff6430eaf31"}, "fileId": "5aOtJuaWVHAopTr6MJ0BOa", "sync": false}, {"__type__": "1e8d666zJJHE6bVnXbR9v1W", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "touchNode": null, "_id": ""}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": false, "_layoutSize": {"__type__": "cc.Size", "width": 5214.02, "height": 2097.04}, "_resize": 1, "_N$layoutType": 0, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 0, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "", "sync": false}]