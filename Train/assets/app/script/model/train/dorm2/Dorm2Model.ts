
import { Dorm2BuildType } from "../../../common/constant/Enums";
import { StateType } from "../../passenger/StateEnum";
import Dorm2Action from "../../passenger/themeActions/Dorm2Action";
import BuildObj from "../common/BuildObj";
import CarriageModel from "../common/CarriageModel";
import Dorm2HearthObj from "./Dorm2HearthObj";
import Dorm2TableObj from "./Dorm2TableObj";
import Dorm2TVObj from "./Dorm2TVObj";

/**
 * 二号宿舍
 */
export default class Dorm2Model extends CarriageModel {

    public isWaterLooping: boolean = false
    public _callback: Function = null

    private _call: Function[] = []

    public getBed() {
        return this.getBuildByOrder(Dorm2BuildType.BED)
    }

    public getHearth() {
        return this.getBuildByOrder(Dorm2BuildType.HEARTH) as Dorm2HearthObj
    }

    public getChairs() {
        return [Dorm2BuildType.CHAIR_1, Dorm2BuildType.CHAIR_2, Dorm2BuildType.CHAIR_3].map((type) => {
            return this.getBuildByOrder(type)
        }).filter(build => !!build)
    }

    public getTable() {
        return this.getBuildByOrder(Dorm2BuildType.TABLE) as Dorm2TableObj
    }

    public getTV() {
        return this.getBuildByOrder(Dorm2BuildType.TV) as Dorm2TVObj
    }

    public newBuildObj(type: Dorm2BuildType) {
        switch (type) {
            case Dorm2BuildType.HEARTH: return new Dorm2HearthObj()
            case Dorm2BuildType.TV: return new Dorm2TVObj()
            case Dorm2BuildType.TABLE: return new Dorm2TableObj()
            default:
                return super.newBuildObj(type)
        }
    }

    public async beforeWaterEnter() {
    }
    public async beforeWaterExit() {
        // 退出之前  检查所有漂浮睡觉的乘客醒来
        return new Promise(resolve => {
            this._callback = resolve
        })
    }

    public afterWaterEnter() {
    }

    public afterWaterExit() {
        this.isWaterLooping = false
    }

    public onWaterLoop() {
        this.isWaterLooping = true
        this._call.forEach(call => call())
        this._call.length = 0
    }

    public waterOnWaterLoop() {
        if (this.isWaterLooping) return true
        return new Promise(resolve => {
            this._call.push(resolve)
        })
    }

    public onFloatSleepEnd() {
        const roles = this.getCheckInRoles()
        for (const role of roles) {
            const state = role.actionAgent.getState(StateType.FLOAT_SLEEP_START) || role.actionAgent.getState(StateType.FLOAT_SLEEP_IDLE) || role.actionAgent.getState(StateType.FLOAT_SLEEP_END)
            if (state) {
                return
            }
        }
        this._callback?.(true)
    }

    public hasFloatSleepRole() {
        const roles = this.getCheckInRoles()
        for (const role of roles) {
            const state = role.actionAgent.getState(StateType.FLOAT_SLEEP_START) || role.actionAgent.getState(StateType.FLOAT_SLEEP_IDLE) || role.actionAgent.getState(StateType.FLOAT_SLEEP_END)
            if (state) {
                return true
            }
        }
        return false
    }

}