import { BUILD_ATTRS, DORM_RIGHT_BED_ID, TIME_LANG } from "../../common/constant/Constant";
import { Condition, FocusCfg, TrainCfg } from "../../common/constant/DataType";
import { BuildAttr, CarriageID, CarriageType, ChangeWorkType, ConditionType, ItemID, ShellAni, TrainBurstTaskType, UIFunctionType, WeakGuideType } from "../../common/constant/Enums";
import { animHelper } from "../../common/helper/AnimHelper";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { dropItemHelper } from "../../common/helper/DropItemHelper";
import { gameHelper } from '../../common/helper/GameHelper';
import { timeHelper } from "../../common/helper/TimeHelper";
import { uiHelper } from "../../common/helper/UIHelper";
import { unlockHelper } from "../../common/helper/UnlockHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import { WeakGuideObj } from "../../model/guide/WeakGuideModel";
import EventType from '../../common/event/EventType';
import NodeType from "../../common/event/NodeType";
import BuildObj from "../../model/train/common/BuildObj";
import BuildCmpt from "../cmpt/build/BuildCmpt";
import CarriageModel from "../../model/train/common/CarriageModel";
import MaskRedCmpt from "../cmpt/common/MaskRedCmpt";
import CarriageLightCmpt from "../cmpt/train/CarriageLightCmpt";
import CarriageFacilityEdit from './CarriageFacilityEdit';
import TrainCarriage from './TrainCarriage';
import PassengerModel from "../../model/passenger/PassengerModel";
import { util } from "../../../core/utils/Utils";
import MainWindCtrl from "../main/MainWindCtrl";
import { resHelper } from "../../common/helper/ResHelper";
import { TrainActivityItem } from "../../model/trainActivity/TrainActivityModel";
import Dorm2Model from "../../model/train/dorm2/Dorm2Model";

const { ccclass, property } = cc._decorator;

@ccclass
export default class TrainShell extends mc.BaseCmptCtrl {
    @property(sp.Skeleton)
    public body: sp.Skeleton = null;

    @property(cc.Node)
    public content: cc.Node = null;

    @property(cc.Node)
    public ui: cc.Node = null;

    @property(cc.Node)
    public nameUI: cc.Node = null;

    @property(cc.Node)
    private uiBtns: cc.Node = null

    @property(cc.Node)
    private btnEngine: cc.Node = null

    @property(cc.Node)
    private btnWater: cc.Node = null

    @property(cc.Node)
    private btnDorm: cc.Node = null

    @property(cc.Node)
    private btnDining: cc.Node = null

    @property(cc.Node)
    private btnBath: cc.Node = null

    @property(cc.Node)
    private build: cc.Node = null

    @property(cc.Node)
    private buildingUI: cc.Node = null

    @property(cc.Node)
    private btnOverBuild: cc.Node = null

    @property(cc.Node)
    private buildUpTips: cc.Node = null

    @property(cc.Node)
    private catDoorNode_: cc.Node = null

    @property(cc.Node)
    buildCostTips: cc.Node = null

    @property(cc.Node)
    transportNode: cc.Node = null

    @property([cc.Node])
    yans: cc.Node[] = []

    @property([cc.Node])
    lizis: cc.Node[] = []

    @property(cc.Node)
    trainActivity: cc.Node = null

    @property(cc.Node)
    activityLizi: cc.Node = null

    @property(cc.Node)
    dorm2Water: cc.Node = null

    private model: CarriageModel = null;

    private showHidingBuilds: Function = null
    private _delayHideWater: () => Promise<void> = null

    public listenEventMaps() {
        return [
            { [EventType.CARRIAGE_EDIT]: this.handleUI },
            { [EventType.GUIDE_UNLOCK_FUNTION]: this.onFunctionUnlock },
            { [EventType.SHOW_WEAK_GUIDE]: this.onWeakGuide },
            { [EventType.SHOW_TRAIN_BTNS]: this.onShowBtns },
            { [EventType.GET_UI_FUNCTION_NODE]: this.getFunctionNode },
            { [EventType.LEVEL_UP_BUILD]: this.onBuildLevelUp },
            { [EventType.SHOW_BUILDUP_TIPS]: this.showBuildUpTips },
            { [EventType.PASSENGER_CHECK_IN_TIPS]: this.showCheckInUpTips },
            { [EventType.TRAIN_CHANGE_WORK_TIPS]: this.showWorkUpTips },
            { [EventType.PLANET_MOVE_CHANGE]: this.updateIdle },
            { [EventType.HIDE_BUILDS]: this.hideBuilds },
            { [EventType.SHOW_BUILDS]: this.showBuilds },
            { [EventType.TRANSPORT_STATE_CHANGE]: this.updateTransport },
            { [EventType.DEBUG]: this.showCarriageUpTips },
            { [EventType.DAILY_REFRESH]: this.onDailyRefresh },
            { [EventType.TRAIN_ACTIVITY_REWARD_SHOW]: this.updateActivityReward },
            { [EventType.TRAIN_ACTIVITY_ANIM_SHOW]: this.updateActivityAnim },
            { [EventType.DAY_NIGHT_EXCHANGE]: this.updateWater },
        ]
    }

    public getId() {
        return this.model?.getID()
    }

    public get canUse() {
        return this.model?.isOpenDoor
    }

    onCreate() {
        this.body.mix2(ShellAni.BuildIdle, ShellAni.MOVE, 0.3)
        this.body.mix(ShellAni.BuildOverIdle, ShellAni.MOVE, 0.3)
        this.body.mix(ShellAni.BuildFront, ShellAni.MOVE, 0.3)
        this.body.mix(ShellAni.BuildAfter, ShellAni.MOVE, 0.3)
    }

    public async init(model: CarriageModel, newCarriage: boolean = false) {
        if (!model) return;
        this.model = model;
        this.content.active = !newCarriage;
        this.buildingUI.active = false
        this.buildUpTips.active = false
        this.buildCostTips.active = false
        this.setOverActive(false)
        this.initBtns()
        this.initGuide()
        this.initNameUI()
        // 增加车厢内容
        this.content.destroyAllChildren();
        this.addCarriage(model)
        this.initWaitAnimation(newCarriage)
        this.updateTransport()
        this.initBurst()
        this.updateActivity()
        this.initWater()
    }

    private initBtns() {
        let id = this.model.getID()
        this.btnEngine.active = id == CarriageID.ENGINE || id == CarriageID.ENGINE2
        this.btnDining.active = id == CarriageID.DINING
        this.btnWater.active = id == CarriageID.WATER
        this.btnDorm.active = this.model.getType() == CarriageType.DORM
        this.btnBath.active = id == CarriageID.BATHROOM
        this.build.Component(MaskRedCmpt).init(id)
        this.btnDorm.Component(MaskRedCmpt).init(id)
        this.initFunc()
    }

    private getBtnById(id: number) {
        if (id == CarriageID.ENGINE || id == CarriageID.ENGINE2) {
            return this.btnEngine
        } else if (id == CarriageID.DINING) {
            return this.btnDining
        } else if (id == CarriageID.WATER) {
            return this.btnWater
        } else if (id == CarriageID.BATHROOM) {
            return this.btnBath
        }
    }

    private initGuide() {
        let id = this.model.getID()
        if (id == CarriageID.DORM) {
            this.addListener(NodeType.GUIDE_BUTTON_BUILD, () => { return this.build.Child('touch') })
            this.addListener(NodeType.GUIDE_BUTTON_CHECKIN_DORM_1, () => { return this.btnDorm.Child('touch') })
        } else if (id == CarriageID.ENGINE) {
            this.addListener(NodeType.GUIDE_SEPECIAL_ENGINE, () => { return this.node })
            this.addListener(NodeType.GUIDE_SEPECIAL_CATDOOR_ENGINE, () => { return this.catDoorNode_ })
            this.addListener(NodeType.GUIDE_BUTTON_BUILD_ENGINE, () => { return this.build.Child('touch') })
            this.addListener(NodeType.GUIDE_BUTTON_WORK_ENGINE_1, () => { return this.btnEngine.Child('touch') })
        }
    }

    private setOverActive(bol: boolean) {
        this.btnOverBuild.active = bol
        this.catDoorNode_.active = bol
    }

    private async initWaitAnimation(newCarriage: boolean = false, isClick: boolean = false) {
        this.updateYans()

        if (newCarriage) {
            if (this.model.isOpenDoor) {
                this.content.active = true
                if (isClick) {
                    await this.body.playAnimation(ShellAni.BuildAfter, false)
                    this.showCarriageUpTips(this.model)
                } else {
                    await this.body.playAnimation(ShellAni.BuildFront, false)
                }
                this.model.setBuildAniOver()
                this.emit(EventType.CARRIAGE_EDIT, false);
            } else {
                return this.playWaitBuild()
            }
        } else {
            if (!this.model.isOpenDoor) {
                return this.showWaitBuild()
            }
            this.emit(EventType.CARRIAGE_EDIT, false);
        }

        this.updateIdle()

        this.content.getComponent(CarriageLightCmpt).setCarriage(this.model)
    }

    public updateIdle() {
        if (!this.model.isOpenDoor) return
        if (gameHelper.planet.isMoving()) {
            this.body.playAnimation(ShellAni.MOVE, true)
            this.updateYans()
        }
        else {
            this.body.playAnimation(ShellAni.BuildIdle, true);
            this.updateYans()
        }
    }

    public addCarriage(model: CarriageModel) {
        let root = this.content

        let prefab = assetsMgr.getPrefab('carriage')
        let node: cc.Node = cc.instantiate2(prefab, root)
        let carriage = node.Component(TrainCarriage)
        carriage.init(model);

        // 建设编辑
        let editPrefab = assetsMgr.getPrefab('carriage_edit');
        let editNode: cc.Node = cc.instantiate2(editPrefab, root);
        editNode.Component(CarriageFacilityEdit).init(model);
        return root
    }

    private handleUI(val: boolean) {
        this.ui.active = !val
        this.nameUI.active = val
        this.Child("topLayer").active = !val
    }

    private initNameUI() {
        this.nameUI.active = false
        this.setLabelName(this.nameUI, this.model.cfg)
    }

    private onShowBtns(val: boolean) {
        this.uiBtns.active = val
    }

    private setLabelName(node: cc.Node, cfg: TrainCfg) {
        let name = node.Child('name', cc.Label)
        name.setLocaleKey(cfg.name)
    }

    public onClickBtnEngine() {
        this.onClickCommon('role/RoleWorkPnl', true)
    }

    public onClickBtnWater() {
        this.onClickCommon('role/RoleWorkPnl', true)
    }

    public onClickBtnDorm() {
        this.onClickCommon('role/RoleCheckInPnl', true)
    }

    public onClickBtnDining() {
        this.onClickCommon('train/DiningMenu')
    }

    public onClickBtnBath() {
        this.onClickCommon('train/BathMenu')
    }

    public onClickBuild() {
        this.onClickCommon('train/SelectBuild', true)
    }

    public async onClickOverBuild() {
        let succ = await this.model.setBuildOverBySever()
        if (!succ) return
        if (!cc.isValid(this)) return
        this.setOverActive(false)
        this.initWaitAnimation(true, true)
    }

    @util.addLock
    private async onClickCommon(pnlName: string, focus = false) {
        if (!this.canUse) return
        eventCenter.emit(EventType.SET_FOCUS, null)

        let p = viewHelper.preloadPnl(pnlName)

        let model = this.model
        let mainWindCtrl = <MainWindCtrl>mc.currWind
        if (focus) {
            this.dorm2Water.opacity = 0
            await mainWindCtrl.focusEdit(model)
        }
        await p
        viewHelper.showPnl(pnlName, model, (needBack: boolean) => {
            this.dorm2Water.opacity = 255
            return mainWindCtrl.focusEditEnd(model, needBack)
        })
    }

    private initFunc() {
        unlockHelper.initFunctionNode(this.getFunctionNode.bind(this))
    }

    public getFunctionNode(type: UIFunctionType) {
        if (type == UIFunctionType.BUILD) {
            return this.build
        } else if (type == UIFunctionType.WORK_ENGINE) {
            if (this.model.getID() == CarriageID.ENGINE) return this.btnEngine
        } else if (type == UIFunctionType.WORK_WATER) {
            if (this.model.getID() == CarriageID.WATER) return this.btnWater
        } else if (type == UIFunctionType.MENU_DINING) {
            if (this.model.getID() == CarriageID.DINING) return this.btnDining
        } else if (type == UIFunctionType.MENU_BATHHOUSE) {
            if (this.model.getID() == CarriageID.BATHROOM) return this.btnBath
            // } else if (type == UIFunctionType.MENU_BALLROOM) {
        } else if (type == UIFunctionType.CHECKIN_DORM_1) {
            if (this.model.getID() == CarriageID.DORM) return this.btnDorm
        } else if (type == UIFunctionType.CHECKIN_DORM_2) {
            if (this.model.getID() == CarriageID.DORM2) return this.btnDorm
        } else if (type == UIFunctionType.CHECKIN_DORM_3) {
            if (this.model.getID() == CarriageID.DORM3) return this.btnDorm
        } else if (type == UIFunctionType.CHECKIN_DORM_4) {
            if (this.model.getID() == CarriageID.DORM4) return this.btnDorm
        }
    }

    public onFunctionUnlock(type) {
        unlockHelper.unlockFunction(this.getFunctionNode.bind(this), type)
    }

    private async playWaitBuild() {
        this.setBuildingUI()
        await this.body.playAnimation(ShellAni.Building)
        if (this.buildingUI.active == true) {
            this.body.playAnimation(ShellAni.BuildingIdle)
        }
    }
    private showWaitBuild() {
        this.setBuildingUI()
        this.body.playAnimation(ShellAni.BuildingIdle)
    }
    private setBuildingUI() {
        this.buildingUI.active = true
        let time = this.buildingUI.Child('time', cc.Label)
        time.unscheduleAllCallbacks()
        time.setLocaleUpdate(this.getTimeText.bind(this))
        time.scheduleUpdate(() => {
            time.Component(cc.LocaleLabel).updateString()
            if (this.model.isCostOver()) {
                time.unscheduleAllCallbacks()
                this.buildingUI.active = false
                this.setOverActive(true)
                this.body.playAnimation(ShellAni.BuildOverIdle, true)
            }
        })
    }
    private getTimeText() {
        let max = this.model.cfg.costTime
        let cur = max - this.model.buildTime / ut.Time.Second
        let bar = this.buildingUI.Child('bar')
        bar.width = bar.Child('max').width * cur / max
        return timeHelper.getTimeShortText(max - Math.floor(cur))
    }

    private onWeakGuide(guide: WeakGuideObj) {
        let needFocus: boolean = false
        let targetNode: cc.Node = null
        if (guide.id == WeakGuideType.BUY_RIGHT_BED_1) {
            targetNode = this.build
            this.checkOverGotoBuyRightBed()
        } else if (guide.id == WeakGuideType.COLLECT_STAR) {
            if (this.model.getID() != CarriageID.DORM) return
            targetNode = dropItemHelper.getItemsByCarriage(this.model).random()
            if (!targetNode) {
                return gameHelper.weakGuide.cancel()
            }
        } else if (guide.id == WeakGuideType.GOTO_BUY_BUILD_1) {
            if (this.model.getID() != gameHelper.weakGuide.buyBuildData?.carriageId) return
            needFocus = true
            targetNode = this.build
            this.checkOverGotoBuyBuild1()
        } else if (guide.id == WeakGuideType.GOTO_LEVELUP_BUILD_1) {
            if (this.model.getID() != gameHelper.weakGuide.levelUpBuildData?.carriageId) return
            needFocus = true
            targetNode = this.build
            this.checkOverGotoLevelUpBuild1()
        } else if (guide.id == WeakGuideType.ASSIGN_WORK_1) {
            let trainId = gameHelper.weakGuide.assignWorkData
            if (this.model.getID() != trainId) return
            this.checkOverAssignWork1()
            targetNode = this.getBtnById(trainId)
            if (!targetNode) return
            needFocus = true
        } else if (guide.id == WeakGuideType.CHARACTER_GETON_1) {
            let role = gameHelper.passenger.getPassenger(gameHelper.weakGuide.characterGetOnData)
            if (!role) return
            if (this.model.getID() != CarriageID.DORM) return
            needFocus = true
            targetNode = this.btnDorm
            this.checkOverGotoCharacterGetOn1()
        }
        if (!targetNode) return
        let root = this.Child("topLayer")
        let pos = ut.convertToNodeAR(targetNode, root)
        animHelper.showWeakGuideFinger(root, guide.fingerGuide, pos)
        if (!needFocus) return
        let focusCfg: FocusCfg = {
            needBackZoomRatio: false,
            needBackX: false,
            needFocusZoomRatio: false,
            backZoomRatio: null,
            time: 0.3,
        }
        eventCenter.emit(EventType.FOCUS_CARRIAGE, this.model, focusCfg)
    }
    private async checkOverGotoBuyRightBed() {
        await viewHelper.waitEnterUI('train/SelectBuildPnl')
        gameHelper.weakGuide.buyBuildStep = 2
        gameHelper.weakGuide.buyBuildData = cfgHelper.getBuildById(DORM_RIGHT_BED_ID)
    }
    private async checkOverGotoBuyBuild1() {
        let event = await viewHelper.waitAnyClickEnd()
        gameHelper.weakGuide.buyBuildStep = event.target == this.build.Child('touch') ? 2 : 0
    }
    private async checkOverGotoLevelUpBuild1() {
        let event = await viewHelper.waitAnyClickEnd()
        gameHelper.weakGuide.levelUpBuildStep = event.target == this.build.Child('touch') ? 2 : 0
    }
    private async checkOverAssignWork1() {
        await viewHelper.waitAnyClickEnd()
        gameHelper.weakGuide.assignWorkStep = 0
    }
    private async checkOverGotoCharacterGetOn1() {
        await viewHelper.waitAnyClickEnd()
        gameHelper.weakGuide.characterGetOnStep = 0
    }
    private actUpTips(root: cc.Node) {
        let startY = root.y
        root.active = true
        root.opacity = 0
        cc.Tween.stopAllByTarget(root)
        cc.tween(root)
            .to(0.3, { y: startY + 120, opacity: 255 }, { easing: cc.easing.sineOut })
            .delay(1)
            .to(0.1, { y: startY + 150, opacity: 0 }, { easing: cc.easing.sineIn })
            .call(() => {
                root.active = false
                root.y = startY
            }).start()
    }

    //显示修建车厢后的收益提升
    private showCarriageUpTips(carriage: CarriageModel) {
        if (this.model.getID() != carriage.getID()) return
        let root = this.buildUpTips
        let content = root.Child("content")
        this.actUpTips(root)
        this.setLabelName(content, this.model.cfg)
        this.setAttrsCarriageUp(content.Child("attrs"), carriage)
        this.setUpTipsLine(content)
    }

    private setAttrsCarriageUp(attrsNode: cc.Node, carriage: CarriageModel) {
        let ary = this.getCarriageUpAttrs(carriage)
        attrsNode.Items(ary, (it, data) => {
            it.Child('icon', cc.MultiFrame).setFrame(data.index)
            let text = it.Child("text")
            text.setLocaleUpdate(() => {
                return `<size=36>${assetsMgr.lang("trainItemBuild_guiText_8")}</size>+${data.val}`
            })
        })
    }

    private getCarriageUpAttrs(carriage: CarriageModel) {
        return [{
            index: 5,
            val: carriage.cfg.load
        }]
    }

    //显示修建设施后的收益提升
    private showBuildUpTips(build: BuildObj) {
        if (this.model.getID() != build.carriageId) return
        let root = this.buildUpTips
        let content = root.Child("content")
        this.actUpTips(root)
        this.setLabelName(content, this.model.cfg)
        this.setAttrsBuildUp(content.Child("attrs"), build)
        this.setUpTipsLine(content)
    }
    //显示入住后的收益提升
    private showCheckInUpTips(roles: PassengerModel[], isChange: boolean) {
        if (isChange) return
        if (!roles || roles.length <= 0) return
        let dormId = roles[0].dormId
        if (this.model.getID() != dormId) return
        let root = this.buildUpTips
        let content = root.Child("content")
        this.actUpTips(root)
        this.setLabelName(content, this.model.cfg)
        this.setAttrsCheckIn(content.Child("attrs"), roles)
        this.setUpTipsLine(content)
    }
    //显示安排工作后的收益提升
    private showWorkUpTips(type: ChangeWorkType, carriageId: number, roleNum: number = 1) {
        if (type != ChangeWorkType.HIRE) return
        if (this.model.getID() != carriageId) return
        let root = this.buildUpTips
        let content = root.Child("content")
        this.actUpTips(root)
        this.setLabelName(content, this.model.cfg)
        this.setAttrsWork(content.Child("attrs"), type, roleNum)
        this.setUpTipsLine(content)

    }
    private setAttrsBuildUp(attrsNode: cc.Node, build: BuildObj) {
        let ary = this.getBuildUpAttrs(build)
        attrsNode.Items(ary, (it, data) => {
            it.Child('icon', cc.MultiFrame).setFrame(data.index)
            let text = it.Child("text")
            text.setLocaleUpdate(() => {
                return `+${data.val}<size=36>/${assetsMgr.lang(TIME_LANG.h)}</>`
            })
        })
    }
    private getBuildUpAttrs(build: BuildObj) {
        let lvCfg = cfgHelper.getBuildLvCfg(build.carriageId, build.order, build.lv)
        let add = lvCfg?.add
        if (!add) return []
        return BUILD_ATTRS.map((attr, index) => {
            let val = add[attr]
            if (val) return { attr, index, val }
        }).filter(m => !!m)
    }
    private setAttrsCheckIn(attrsNode: cc.Node, roles: PassengerModel[]) {
        let attr: BuildAttr[] = []
        roles.forEach(e => {
            attr.push(e.getCheckInBuildAttr())
        })
        let rate = `+${cfgHelper.getMiscData("checkInOutputRate") * 100}%`
        attrsNode.Items(attr, (it, data) => {
            it.Child('icon', cc.MultiFrame).setFrame(data == BuildAttr.STAR ? 0 : 1)
            let text = it.Child("text")
            text.setLocaleUpdate(() => {
                return rate
            })
        })
    }
    private setAttrsWork(attrsNode: cc.Node, type: ChangeWorkType, roleNum: number) {
        let id = this.model.getID()
        let index = 0
        if (id == CarriageID.ENGINE || id == CarriageID.ENGINE2) {
            index = 3
        } else if (id == CarriageID.WATER) {
            index = 2
        }
        let ary = [{ index }]
        let rate = `${type == ChangeWorkType.FIRE ? '-' : '+'}${cfgHelper.getMiscData("workOutputRate") * 100 * roleNum}%`
        attrsNode.Items(ary, (it, data) => {
            it.Child('icon', cc.MultiFrame).setFrame(data.index)
            let text = it.Child("text")
            text.setLocaleUpdate(() => {
                return rate
            })
        })
    }

    private async setUpTipsLine(content: cc.Node) {
        await ut.waitNextFrame(1, this)
        let lineNode = content.Child("line")
        let attrsNode = content.Child("attrs")
        if (!lineNode.Data) lineNode.Data = lineNode.width
        lineNode.width = Math.max(lineNode.Data, attrsNode.width)
    }

    private onBuildLevelUp(build: BuildObj) {
        if (this.model == null) return;
        if (build.carriageId != this.model.getID()) return;

        this.showBuildLevelUpTips(build)
    }

    private showBuildLevelUpTips(build: BuildObj) {
        if (build.lv >= build.getMaxLv()) return
        let facilityNode = this.content.Child('carriageEdit/facility')
        let fboPrefab = this.Child('tips/flashBuild')
        let buildNode = facilityNode.Child(`trainItem_${build.carriageId}_${build.skin}_${build.order}`)
        if (!!buildNode) {
            buildNode.Component(BuildCmpt).playFlash(fboPrefab)
        }
        let prefab = this.Child('tips/buildLvUpTips')
        let it = cc.instantiate2(prefab, this.Child('tips'))
        let pos = ut.convertToNodeAR(facilityNode, it.parent, build.position)
        let currencyNode = eventCenter.get(NodeType.SELECTBUILD_CURRENCY)
        let currencyPos = ut.convertToNodeAR(currencyNode, it.parent, null, null, true)
        pos.y = Math.min(pos.y, currencyPos.y - currencyNode.height / 2 - 150)
        it.active = true
        it.setPosition(pos)

        let ary = this.getBuildUpAttrs(build)
        it.Items(ary, (it, data) => {
            it.Child('icon', cc.MultiFrame).setFrame(data.index)
            uiHelper.setBuildAttrsText(it.Child('text'), data.attr, data.val)
        })

        let startY = it.y
        it.opacity = 0
        cc.Tween.stopAllByTarget(it)
        //飘出
        cc.tween(it)
            .delay(0.35)
            .to(0.3, { y: startY + 120, opacity: 255 }, { easing: cc.easing.sineOut })
            .delay(1)
            .to(0.1, { y: startY + 150, opacity: 0 }, { easing: cc.easing.sineIn })
            .call(() => {
                it.destroy()
            }).start()
    }

    private updateYans() {
        let isOpenDoor = this.model.isOpenDoor
        let isMoving = gameHelper.planet.isMoving()
        for (let lizi of this.lizis) {
            if (isOpenDoor && isMoving) {
                lizi.active = true
                lizi.Component(cc.ParticleSystem).resetSystem()
            }
            else {
                lizi.active = false
                lizi.Component(cc.ParticleSystem).stopSystem()
            }
        }
        for (let yan of this.yans) {
            yan.active = isOpenDoor && !gameHelper.planet.isMoving()
        }
    }

    //todo 修建/更改设施时需要隐藏隐藏数组中的设施，让设施修建动画完全被看到。
    private hideBuilds(build: BuildObj) {
        if (!build) {
            return
        }
        let builds: cc.Node[] = []
        let facilityNode = this.content.Child('carriage/facility')
        facilityNode.children.forEach(node => {
            if (build.json?.hideOrder?.has(this.getOrder(node.name))) {
                builds.push(node)
                cc.tween(node).to(0.2, { opacity: 0 }).start()
            }
        })
        if (!!this.showHidingBuilds) {
            this.showHidingBuilds(0)
            this.showHidingBuilds = null
        }
        if (builds.length > 0 && !!builds[0]) {
            this.showHidingBuilds = (time = 0.2) => {
                builds.forEach(node => {
                    cc.tween(node).to(time, { opacity: 255 }).start()
                })
            }
        }
    }

    private showBuilds() {
        if (!!this.showHidingBuilds) {
            this.showHidingBuilds()
            this.showHidingBuilds = null
        }
    }

    private updateTransport() {
        // let is = gameHelper.transport.isTransporting() && this.model.getID() == 1013
        let is = gameHelper.transport.isTransporting()
        if (is) {
            const cur = gameHelper.transport.getLoad()
            const { min, max } = this.model.getLoadRange()

            if (cur >= max) {
                this.transportNode.Child("bg", cc.MultiFrame).setFrame(1)
            } else if (cur > min) {
                this.transportNode.Child("bg", cc.MultiFrame).setFrame(0)
            } else {
                is = false
            }
        }
        this.transportNode.active = is
    }

    private getOrder(name: string) {
        let list = name.split('_')
        return Number(list[3])
    }

    private onDailyRefresh() {
        this.updateTransport()
    }

    private async initBurst() {
        let task = this.model.getTrainBurstTask()
        let type = task?.id
        let root = this.Child("ui/burstEffect")
        let tag = this.getTag()
        let node = null
        switch (type) {
            case TrainBurstTaskType.FIRE:
            case TrainBurstTaskType.POWER:
            case TrainBurstTaskType.METEOR:
                node = await resHelper.loadBurstEffect(type, root, tag)
                if (!cc.isValid(this)) return
                break
        }
        if (!task) {
            return
        }

        switch (type) {
            case TrainBurstTaskType.FIRE:
                node.Component(sp.Skeleton).playAnimation("idle_1", true)
                break
            case TrainBurstTaskType.METEOR:
                break
        }
    }

    private updateActivityReward() {
        const list = gameHelper.trainActivity.getRewardShowWhithTrain(this.model.getID())
        const bol = list.length > 0
        const activityRewardNode = this.trainActivity.Child("reward")
        activityRewardNode.active = bol
        // 奖励列表
        if (bol) {
            activityRewardNode.Items(list, (it: cc.Node, data, index: number) => {
                it.active = true
                let bgIndex = 0
                const rewardType = data.rewardType
                const rewardId = data.rewardId
                switch (true) {
                    case rewardType == ConditionType.STAR_DUST:
                        bgIndex = 0
                        break
                    case rewardType == ConditionType.HEART:
                        bgIndex = 1
                        break
                    case rewardType == ConditionType.PROP && rewardId == ItemID.ELECTRIC:
                        bgIndex = 3
                        break
                    case rewardType == ConditionType.PROP && rewardId == ItemID.WATER:
                        bgIndex = 4
                        break
                    case rewardType == ConditionType.PROP && rewardId == ItemID.VITALITY:
                        bgIndex = 2
                        break
                }
                it.Component(cc.MultiFrame).setFrame(bgIndex)
                it.off("click")
                it.on("click", () => {
                    gameHelper.trainActivity.claimReward(data)
                })
            })
        }
    }

    private updateActivityAnim() {
        // 正在进行的活动
        const cur = gameHelper.trainActivity.currentActivity
        const spineNode = this.trainActivity.Child("spine")
        if (!cur || cur.trainId != this.model.getID()) {
            spineNode.active = false
            this.activityLizi.active = false
            return
        }
        const showSpine = !!(cur && cur.cfg.spine)
        const showLizi = !!(cur && cur.cfg.lizi?.length)
        spineNode.active = showSpine
        this.activityLizi.active = showLizi
        if (showSpine) {
            const skeleton = spineNode.Component(sp.Skeleton)
            const url = `train/carriage/activity/${cur.cfg.spine}`
            if (!skeleton.skeletonData || skeleton.skeletonData.name != cur.cfg.spine) {
                resHelper.loadSkeletonData(skeleton, { url, tag: this.getTag() }).then(() => {
                    skeleton.setSkin("default")
                    skeleton.playAnimation("loop", true)
                })
            }
        }
        if (showLizi) {
            this.activityLizi.children.forEach(it => it.active = cur.cfg.lizi.includes(it.name))
        }
    }

    // 列车活动
    private updateActivity() {
        this.updateActivityAnim()
        this.updateActivityReward()
    }

    private async initWater() {
        const isdorm2 = this.model.getID() == CarriageID.DORM2
        if (!isdorm2) {
            return void (this.dorm2Water.active = false)
        }
        const model = this.model as Dorm2Model
        // 如果有漂浮的乘客睡觉，也要show water
        this.dorm2Water.active = gameHelper.world.isNight() || model.hasFloatSleepRole()
        if (!this.dorm2Water.active) return
        await this.checkWaterItem()
        const sk = this.dorm2Water.Child("body", sp.Skeleton)
        sk.playAnimation("loop", true);
        model.onWaterLoop()
    }

    private async checkWaterItem() {
        const waterNode = this.dorm2Water.Child("water")
        if (waterNode.childrenCount != 2) {
            waterNode.Items(new Array(2), (it, data, index) => { })
            const lyt = waterNode.Component(cc.Layout)
            lyt.updateLayout()
            lyt.enabled = false
        }
        let to = waterNode.y == 0 ? -800 : 0
        cc.Tween.stopAllByTarget(waterNode)
        await cc.tween(waterNode).to(1, { y: to }).start().promise()
    }

    private async updateWater() {
        const isdorm2 = this.model.getID() == CarriageID.DORM2
        if (!isdorm2) return
        const model = this.model as Dorm2Model
        const isNight = gameHelper.world.isNight()

        const logic = async () => {
            if (!this.dorm2Water) return
            this.dorm2Water.active = true
            await this.checkWaterItem()
            const sk = this.dorm2Water.Child("body", sp.Skeleton)
            const anim1 = isNight ? "enter" : "exit"
            const anim2 = isNight ? "loop" : ""
            await sk.playAnimation(anim1)
            isNight ? model.afterWaterEnter() : model.afterWaterExit()
            if (anim2) {
                model.onWaterLoop()
                return void await sk.playAnimation(anim2, true)
            }
            this.dorm2Water.active = false
        }
        if (isNight) {
            await model.beforeWaterEnter()
            return void await logic()
        }
        await model.beforeWaterExit()
        console.log("可以停水了...")
        await logic()
    }

    private playerWaterWave() {
        const isdorm2 = this.model.getID() == CarriageID.DORM2
        if (!isdorm2) return
        if (!this.dorm2Water.active) return
        const waterNode = this.dorm2Water.Child("water")
        const children = waterNode.children
        if (children.length !== 2) return
        const item1 = children[0]
        const item2 = children[1]
        const waveSpeed = 70
        const itemWidth = item1.width
        item1.x += waveSpeed * cc.director.getDeltaTime()
        item2.x += waveSpeed * cc.director.getDeltaTime()
        const rightBoundary = itemWidth
        if (item1.x >= rightBoundary) {
            item1.x = item2.x - itemWidth
        }
        if (item2.x >= rightBoundary) {
            item2.x = item1.x - itemWidth
        }
    }

    update(_dt: number): void {
        this.playerWaterWave()
    }

}
